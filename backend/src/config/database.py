"""
Database configuration and connection setup for MongoDB using AsyncMongoClient
"""
import os
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """Async Database configuration and connection management"""

    def __init__(self):
        self.mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        self.database_name = os.getenv("DATABASE_NAME", "chatbot_system")
        self.client: MongoClient = None
        self.db = None

    def connect(self):
        """Establish connection to MongoDB"""
        try:
            self.client = MongoClient(self.mongo_uri)
            # Test the connection
            self.client.admin.command('ping')
            self.db = self.client[self.database_name]
            logger.info(f"Successfully connected to MongoDB: {self.database_name}")
            return True
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            return False

    def get_database(self):
        """Get database instance"""
        if self.db is None:
            self.connect()
        return self.db

    def get_database_sync(self):
        """Get database instance synchronously (assumes connection is already established)"""
        return self.db

    def get_collection(self, collection_name: str):
        """Get specific collection"""
        db = self.get_database()
        return db[collection_name]

    def close_connection(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            logger.info("Database connection closed")

# Global database instance
db_config = DatabaseConfig()

def get_db():
    """Get database instance"""
    return db_config.get_database()

def get_collection(collection_name):
    """Get specific collection"""
    return db_config.get_collection(collection_name)

# Collection names
COLLECTIONS = {
    'users': 'users',
    'messages': 'messages',
    'conversations': 'conversations',
    'bookings': 'bookings',
    'time_slots': 'time_slots',
    'analytics': 'analytics'
}

def initialize_collections():
    """Initialize collections with indexes and constraints"""
    db = get_db()

    # Users collection indexes
    users_collection = db[COLLECTIONS['users']]
    users_collection.create_index("email", unique=True)
    users_collection.create_index("phone", unique=True)

    # Messages collection indexes
    messages_collection = db[COLLECTIONS['messages']]
    messages_collection.create_index([("user_id", 1), ("timestamp", -1)])
    messages_collection.create_index("session_id")
    messages_collection.create_index("conversation_id")
    messages_collection.create_index("reply_to_message_id")
    messages_collection.create_index("thread_id")

    # Conversations collection indexes
    conversations_collection = db[COLLECTIONS['conversations']]
    conversations_collection.create_index("session_id", unique=True)
    conversations_collection.create_index([("participants.user_id", 1), ("last_message_at", -1)])
    conversations_collection.create_index([("status", 1), ("updated_at", -1)])

    # Bookings collection indexes
    bookings_collection = db[COLLECTIONS['bookings']]
    bookings_collection.create_index([("user_id", 1), ("booking_date", -1)])
    bookings_collection.create_index([("date", 1), ("time", 1)], unique=True)

    # Time slots collection indexes
    time_slots_collection = db[COLLECTIONS['time_slots']]
    time_slots_collection.create_index([("date", 1), ("time", 1)], unique=True)

    # Analytics collection indexes
    analytics_collection = db[COLLECTIONS['analytics']]
    analytics_collection.create_index([("date", 1), ("metric_type", 1)])

    logger.info("Collections and indexes initialized successfully")

def create_sample_time_slots():
    """Create sample time slots for booking system"""
    from datetime import datetime, timedelta

    time_slots_collection = get_collection(COLLECTIONS['time_slots'])

    # Clear existing slots
    time_slots_collection.delete_many({})

    # Create slots for next 30 days
    base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    slots = []

    for i in range(30):  # Next 30 days
        date = base_date + timedelta(days=i)
        # Skip weekends
        if date.weekday() < 5:  # Monday=0, Friday=4
            for hour in [9, 11, 14, 16]:  # 9AM, 11AM, 2PM, 4PM
                slot_datetime = date.replace(hour=hour)
                slots.append({
                    "date": slot_datetime.strftime("%Y-%m-%d"),
                    "time": slot_datetime.strftime("%H:%M"),
                    "datetime": slot_datetime,
                    "available": True,
                    "max_bookings": 5,  # Allow multiple bookings per slot
                    "current_bookings": 0,
                    "created_at": datetime.utcnow()
                })

    if slots:
        time_slots_collection.insert_many(slots)
        logger.info(f"Created {len(slots)} time slots")

def main():
    """Main function for testing database setup"""
    if db_config.connect():
        initialize_collections()
        create_sample_time_slots()
        print("Database setup completed successfully!")
    else:
        print("Failed to setup database")

if __name__ == "__main__":
    main()
