"""
Database configuration and connection setup for MongoDB using AsyncMongoClient
"""
import os
from pymongo import AsyncMongoClient
from pymongo.errors import ConnectionFailure
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """Database configuration and connection management with async support"""

    def __init__(self):
        self.mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        self.database_name = os.getenv("DATABASE_NAME", "chatbot_system")
        self.async_client: AsyncMongoClient = None
        self.async_db = None
        # Keep sync client for legacy services
        from pymongo import MongoClient
        self.sync_client: MongoClient = None
        self.sync_db = None

    async def connect(self):
        """Establish async connection to MongoDB"""
        try:
            self.async_client = AsyncMongoClient(self.mongo_uri)
            # Test the connection
            await self.async_client.admin.command('ping')
            self.async_db = self.async_client[self.database_name]
            logger.info(f"Successfully connected to MongoDB (async): {self.database_name}")
            return True
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB (async): {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB (async): {e}")
            return False

    def connect_sync(self):
        """Establish sync connection to MongoDB"""
        try:
            from pymongo import MongoClient
            self.sync_client = MongoClient(self.mongo_uri)
            # Test the connection
            self.sync_client.admin.command('ping')
            self.sync_db = self.sync_client[self.database_name]
            logger.info(f"Successfully connected to MongoDB (sync): {self.database_name}")
            return True
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB (sync): {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB (sync): {e}")
            return False

    async def get_database(self):
        """Get async database instance"""
        if self.async_db is None:
            await self.connect()
        return self.async_db

    def get_database_sync(self):
        """Get sync database instance"""
        if self.sync_db is None:
            self.connect_sync()
        return self.sync_db

    async def get_collection(self, collection_name: str):
        """Get async collection"""
        db = await self.get_database()
        return db[collection_name]

    def get_collection_sync(self, collection_name: str):
        """Get sync collection"""
        db = self.get_database_sync()
        return db[collection_name]

    async def close_connection(self):
        """Close database connections"""
        if self.async_client:
            self.async_client.close()
            logger.info("Async database connection closed")
        if self.sync_client:
            self.sync_client.close()
            logger.info("Sync database connection closed")

# Global database instance
db_config = DatabaseConfig()

async def get_db():
    """Get database instance"""
    return await db_config.get_database()

async def get_collection(collection_name):
    """Get async collection"""
    return await db_config.get_collection(collection_name)

def get_collection_sync(collection_name):
    """Get sync collection"""
    return db_config.get_collection_sync(collection_name)

# Collection names
COLLECTIONS = {
    'users': 'users',
    'messages': 'messages',
    'conversations': 'conversations',
    'bookings': 'bookings',
    'time_slots': 'time_slots',
    'analytics': 'analytics'
}

async def initialize_collections():
    """Initialize collections with indexes and constraints"""
    # Users collection indexes
    users_collection = await get_collection(COLLECTIONS['users'])
    await users_collection.create_index("email", unique=True)
    await users_collection.create_index("phone", unique=True)

    # Messages collection indexes
    messages_collection = await get_collection(COLLECTIONS['messages'])
    await messages_collection.create_index([("user_id", 1), ("timestamp", -1)])
    await messages_collection.create_index("session_id")
    await messages_collection.create_index("conversation_id")
    await messages_collection.create_index("reply_to_message_id")
    await messages_collection.create_index("thread_id")

    # Conversations collection indexes
    conversations_collection = await get_collection(COLLECTIONS['conversations'])
    await conversations_collection.create_index("session_id", unique=True)
    await conversations_collection.create_index([("participants.user_id", 1), ("last_message_at", -1)])
    await conversations_collection.create_index([("status", 1), ("updated_at", -1)])

    # Bookings collection indexes
    bookings_collection = await get_collection(COLLECTIONS['bookings'])
    await bookings_collection.create_index([("user_id", 1), ("booking_date", -1)])
    await bookings_collection.create_index([("date", 1), ("time", 1)], unique=True)

    # Time slots collection indexes
    time_slots_collection = await get_collection(COLLECTIONS['time_slots'])
    await time_slots_collection.create_index([("date", 1), ("time", 1)], unique=True)

    # Analytics collection indexes
    analytics_collection = await get_collection(COLLECTIONS['analytics'])
    await analytics_collection.create_index([("date", 1), ("metric_type", 1)])

    logger.info("Collections and indexes initialized successfully")

async def create_sample_time_slots():
    """Create sample time slots for booking system"""
    from datetime import datetime, timedelta, timezone

    time_slots_collection = await get_collection(COLLECTIONS['time_slots'])

    # Clear existing slots
    await time_slots_collection.delete_many({})

    # Create slots for next 30 days
    base_date = datetime.now(timezone.utc).replace(hour=9, minute=0, second=0, microsecond=0)
    slots = []

    for i in range(30):  # Next 30 days
        date = base_date + timedelta(days=i)
        # Skip weekends
        if date.weekday() < 5:  # Monday=0, Friday=4
            for hour in [9, 11, 14, 16]:  # 9AM, 11AM, 2PM, 4PM
                slot_datetime = date.replace(hour=hour)
                slots.append({
                    "date": slot_datetime.strftime("%Y-%m-%d"),
                    "time": slot_datetime.strftime("%H:%M"),
                    "datetime": slot_datetime,
                    "available": True,
                    "max_bookings": 5,  # Allow multiple bookings per slot
                    "current_bookings": 0,
                    "created_at": datetime.now(timezone.utc)
                })

    if slots:
        await time_slots_collection.insert_many(slots)
        logger.info(f"Created {len(slots)} time slots")

async def main():
    """Main function for testing database setup"""
    if await db_config.connect():
        await initialize_collections()
        await create_sample_time_slots()
        print("Database setup completed successfully!")
    else:
        print("Failed to setup database")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
