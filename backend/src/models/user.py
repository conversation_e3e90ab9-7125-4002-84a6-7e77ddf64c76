"""
User model for the chat system
"""
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, List
from pydantic import BaseModel, EmailStr, Field
from bson import ObjectId
import argon2
from argon2 import PasswordHasher

from pydantic import GetJsonSchemaHandler
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import core_schema
from typing import Any

class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: Any
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate),
                ])
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            ),
        )

    @classmethod
    def validate(cls, v):
        if isinstance(v, ObjectId):
            return v
        if isinstance(v, str) and ObjectId.is_valid(v):
            return ObjectId(v)
        raise ValueError("Invalid ObjectId")

    @classmethod
    def __get_pydantic_json_schema__(
        cls, schema: core_schema.CoreSchema, handler: GetJsonSchemaHandler
    ) -> JsonSchemaValue:
        return {"type": "string"}

class TenantInfo(BaseModel):
    """Tenant information for multi-tenancy support"""
    tenant_id: str
    tenant_name: str
    tenant_type: str = "individual"  # individual, organization, enterprise
    subscription_plan: str = "free"  # free, pro, enterprise
    max_conversations: int = 100
    max_messages_per_day: int = 1000
    features: List[str] = Field(default_factory=list)

class UserBase(BaseModel):
    """Base user model"""
    name: str = Field(..., min_length=2, max_length=100)
    email: EmailStr
    phone: str = Field(..., min_length=10, max_length=15)
    tenant_info: Optional[TenantInfo] = None
    
class UserCreate(UserBase):
    """User creation model"""
    password: str = Field(..., min_length=6)

class UserUpdate(BaseModel):
    """User update model"""
    name: Optional[str] = Field(None, min_length=2, max_length=100)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, min_length=10, max_length=15)

class UserInDB(UserBase):
    """User model as stored in database"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    hashed_password: str
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    preferences: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class UserResponse(UserBase):
    """User response model (without sensitive data)"""
    id: str
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    preferences: Dict[str, Any] = Field(default_factory=dict)

class CurrentUser:
    """Enhanced current user class with database access and tenant info"""

    def __init__(self, user_data: UserInDB, db_collections: Dict[str, Any] = None):
        self.id = user_data.id
        self.name = user_data.name
        self.email = user_data.email
        self.phone = user_data.phone
        self.tenant_info = user_data.tenant_info
        self.is_active = user_data.is_active
        self.created_at = user_data.created_at
        self.updated_at = user_data.updated_at
        self.last_login = user_data.last_login
        self.preferences = user_data.preferences
        self.hashed_password = user_data.hashed_password

        # Database collections for easy access
        self._db_collections = db_collections or {}

    @property
    def tenant_id(self) -> Optional[str]:
        """Get tenant ID"""
        return self.tenant_info.tenant_id if self.tenant_info else None

    @property
    def subscription_plan(self) -> str:
        """Get subscription plan"""
        return self.tenant_info.subscription_plan if self.tenant_info else "free"

    @property
    def max_conversations(self) -> int:
        """Get max conversations allowed"""
        return self.tenant_info.max_conversations if self.tenant_info else 100

    @property
    def max_messages_per_day(self) -> int:
        """Get max messages per day allowed"""
        return self.tenant_info.max_messages_per_day if self.tenant_info else 1000

    def has_feature(self, feature: str) -> bool:
        """Check if user has a specific feature"""
        if not self.tenant_info:
            return False
        return feature in self.tenant_info.features

    async def get_db_collection(self, collection_name: str):
        """Get database collection (async)"""
        if collection_name in self._db_collections:
            return self._db_collections[collection_name]

        # Import here to avoid circular imports
        from src.config.database import get_collection
        collection = await get_collection(collection_name)
        self._db_collections[collection_name] = collection
        return collection

    async def get_user_conversations_count(self) -> int:
        """Get count of user's conversations"""
        try:
            conversations_collection = await self.get_db_collection("conversations")
            count = await conversations_collection.count_documents({
                "participants.user_id": str(self.id),
                "status": {"$ne": "deleted"}
            })
            return count
        except Exception:
            return 0

    async def get_user_messages_today_count(self) -> int:
        """Get count of user's messages today"""
        try:
            from datetime import datetime, timezone
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

            messages_collection = await self.get_db_collection("messages")
            count = await messages_collection.count_documents({
                "user_id": str(self.id),
                "timestamp": {"$gte": today_start}
            })
            return count
        except Exception:
            return 0

    async def can_create_conversation(self) -> bool:
        """Check if user can create a new conversation"""
        current_count = await self.get_user_conversations_count()
        return current_count < self.max_conversations

    async def can_send_message(self) -> bool:
        """Check if user can send a message today"""
        today_count = await self.get_user_messages_today_count()
        return today_count < self.max_messages_per_day

    def to_response(self) -> UserResponse:
        """Convert to UserResponse model"""
        return UserResponse(
            id=str(self.id),
            name=self.name,
            email=self.email,
            phone=self.phone,
            tenant_info=self.tenant_info,
            is_active=self.is_active,
            created_at=self.created_at,
            last_login=self.last_login,
            preferences=self.preferences
        )

class UserService:
    """User service for database operations"""

    def __init__(self):
        self.ph = PasswordHasher()
        self.collection = None

    async def _get_collection(self):
        """Get users collection"""
        if self.collection is None:
            from src.config.database import get_collection, COLLECTIONS
            self.collection = await get_collection(COLLECTIONS['users'])
        return self.collection

    def hash_password(self, password: str) -> str:
        """Hash password using Argon2"""
        return self.ph.hash(password)

    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        try:
            self.ph.verify(hashed_password, password)
            return True
        except argon2.exceptions.VerifyMismatchError:
            return False
        except Exception:
            return False
    
    async def create_user(self, user_data: UserCreate) -> UserInDB:
        """Create new user"""
        collection = await self._get_collection()

        # Check if user already exists
        existing_user = await collection.find_one({
            "$or": [
                {"email": user_data.email},
                {"phone": user_data.phone}
            ]
        })

        if existing_user:
            raise ValueError("User with this email or phone already exists")

        # Hash password
        hashed_password = self.hash_password(user_data.password)

        # Create default tenant info if not provided
        tenant_info = user_data.tenant_info
        if not tenant_info:
            import uuid
            tenant_info = TenantInfo(
                tenant_id=str(uuid.uuid4()),
                tenant_name=f"{user_data.name}'s Workspace",
                tenant_type="individual",
                subscription_plan="free",
                max_conversations=100,
                max_messages_per_day=1000,
                features=["basic_chat", "conversation_history"]
            )

        # Create user document
        user_doc = {
            "name": user_data.name,
            "email": user_data.email,
            "phone": user_data.phone,
            "tenant_info": tenant_info.model_dump() if tenant_info else None,
            "hashed_password": hashed_password,
            "is_active": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "preferences": {}
        }

        result = await collection.insert_one(user_doc)
        user_doc["_id"] = result.inserted_id

        return UserInDB(**user_doc)
    
    async def get_user_by_email(self, email: str) -> Optional[UserInDB]:
        """Get user by email"""
        collection = await self._get_collection()
        user_doc = await collection.find_one({"email": email})
        if user_doc:
            return UserInDB(**user_doc)
        return None

    async def get_user_by_id(self, user_id: str) -> Optional[UserInDB]:
        """Get user by ID"""
        try:
            collection = await self._get_collection()
            user_doc = await collection.find_one({"_id": ObjectId(user_id)})
            if user_doc:
                return UserInDB(**user_doc)
        except:
            pass
        return None
    
    async def update_user(self, user_id: str, update_data: UserUpdate) -> Optional[UserInDB]:
        """Update user"""
        try:
            update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
            if update_dict:
                update_dict["updated_at"] = datetime.utcnow()

                result = await self.collection.update_one(
                    {"_id": ObjectId(user_id)},
                    {"$set": update_dict}
                )

                if result.modified_count:
                    return await self.get_user_by_id(user_id)
        except:
            pass
        return None

    async def update_last_login(self, user_id: str):
        """Update user's last login time"""
        try:
            await self.collection.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": {"last_login": datetime.utcnow()}}
            )
        except:
            pass

    async def authenticate_user(self, email: str, password: str) -> Optional[UserInDB]:
        """Authenticate user with email and password"""
        user = await self.get_user_by_email(email)
        if user and self.verify_password(password, user.hashed_password):
            await self.update_last_login(str(user.id))
            return user
        return None
